# Guide de Migration du Style ox_inventory

## Modifications Apportées

### 1. Styles de Base
- **Polices** : Ajout des polices Poppins d'ox_inventory
- **Variables CSS** : Intégration des variables CSS d'ox_inventory
- **Animations** : Ajout des animations `shake` et `openmenu`

### 2. Structure de l'Inventaire
- **Grille** : Conversion vers un système de grille CSS Grid
- **Dimensions** : Adaptation aux dimensions d'ox_inventory (98px x 95px par slot)
- **Scrollbar** : Scrollbar personnalisée avec couleur violette (#5c1c88)

### 3. Styles des Slots
- **Apparence** : Background noir semi-transparent comme ox_inventory
- **Transitions** : Effets de hover et animations fluides
- **Taille** : Standardisation à 98px x 95px

### 4. Styles des Items
- **Positionnement** : Items centrés dans les slots
- **Noms** : Style des noms d'items adapté
- **Numérotation** : Ajout du système de numérotation des slots

### 5. Contrôles
- **Boutons** : Style moderne avec effets de hover
- **Layout** : Disposition centrée et responsive
- **Animations** : Effets de clic et hover

### 6. Hotbar
- **Position** : Centré en bas de l'écran
- **Taille** : Slots de 10.2vh comme ox_inventory
- **Espacement** : Gap de 2px entre les slots

### 7. Animations et Effets
- **Drag & Drop** : Animation de shake pendant le drag
- **Hover** : Effets de scale et changement de couleur
- **Transitions** : Transitions fluides sur tous les éléments

## Couleurs Principales
- **Violet principal** : #5c1c88 (scrollbar, numéros de slots)
- **Background slots** : rgba(0, 0, 0, .4)
- **Background hover** : rgba(0, 0, 0, .6)
- **Texte** : #fff (blanc)

## Fichiers Modifiés
- `resources\inventaire\html\css\ui.css` (principal)
- `resources\inventaire\html\css\ui_backup.css` (sauvegarde)

## Notes
- Le style est maintenant plus proche d'ox_inventory
- Les animations sont plus fluides
- L'interface est plus moderne et responsive
- Compatibilité maintenue avec la structure HTML existante
