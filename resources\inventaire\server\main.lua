function sendToDiscordWithSpecialURL(name, message, color, url)
    local DiscordWebHook = url
    -- Modify here your discordWebHook username = name, content = message,embeds = embeds
  
  local embeds = {
      {
		["color"] = color,
		["title"] = name,
		["description"] = message,
		["footer"] = {["text"] = os.date("%Y/%m/%d %H:%M:%S")}, 
      }
  }
    if message == nil or message == '' then
		return
	end
    PerformHttpRequest(DiscordWebHook, function(err, text, headers) end, 'POST', json.encode({ username = name,embeds = embeds}), { ['Content-Type'] = 'application/json' })
end

ESX.RegisterServerCallback("esx_inventoryhud:getPlayerInventory", function(source, cb, target)
	local targetXPlayer = ESX.GetPlayerFromId(target)
	if targetXPlayer ~= nil then
		MySQL.Async.fetchAll("SELECT * FROM card_account WHERE porteur = '" .. targetXPlayer.identifier .. "'", {}, function(result)
			MySQL.Async.fetchAll("SELECT * FROM id_card WHERE porteur = '" .. targetXPlayer.identifier .. "'", {}, function(result2)
				if targetXPlayer ~= nil then
					cb({inventory = targetXPlayer.inventory, money = targetXPlayer.getMoney(), accounts = targetXPlayer.accounts, weapons = targetXPlayer.loadout, weight = targetXPlayer.getWeight(), maxWeight = targetXPlayer.maxWeight, cards = result, idcard = result2})
				else
					cb(nil)
				end
			end)
		end)
	end
end)

ESX.RegisterServerCallback("esx_inventoryhud:othersPlayerInventory", function(source, cb, target)
	local targetXPlayer = ESX.GetPlayerFromId(target)
	if targetXPlayer ~= nil then
		MySQL.Async.fetchAll("SELECT * FROM card_account WHERE porteur = '" .. targetXPlayer.identifier .. "'", {}, function(result)
			MySQL.Async.fetchAll("SELECT * FROM id_card WHERE porteur = '" .. targetXPlayer.identifier .. "'", {}, function(result2)
				MySQL.Async.fetchAll("SELECT * FROM vetement WHERE identifier = '" .. targetXPlayer.identifier .. "'", {}, function(result3)
					if targetXPlayer ~= nil then
						cb({inventory = targetXPlayer.inventory, money = targetXPlayer.getMoney(), accounts = targetXPlayer.accounts, weapons = targetXPlayer.loadout, weight = targetXPlayer.getWeight(), maxWeight = targetXPlayer.maxWeight, cards = result, idcard = result2, kevlar = result3})
					else
						cb(nil)
					end
				end)
			end)
		end)
	end
end)

RegisterNetEvent("esx_inventoryhud:tradePlayerItem")
AddEventHandler("esx_inventoryhud:tradePlayerItem",	function(from, target, type, itemName, itemCount)
	local _source = from

	local sourceXPlayer = ESX.GetPlayerFromId(_source)
	local targetXPlayer = ESX.GetPlayerFromId(target)

	if type == "item_standard" then
		local sourceItem = sourceXPlayer.getInventoryItem(itemName)
		local targetItem = targetXPlayer.getInventoryItem(itemName)

		if itemCount > 0 and sourceItem.count >= itemCount then
			if targetXPlayer.canCarryItem(itemName, itemCount) then

				sourceXPlayer.removeInventoryItem(itemName, itemCount)
				targetXPlayer.addInventoryItem(itemName, itemCount)
			else
				sourceXPlayer.showNotification('~r~Impossible~s~~n~l\'inventaire de l\'individu est plein.')
			end
		end
	elseif type == "item_money" then
		if itemCount > 0 and sourceXPlayer.getMoney() >= itemCount then
			sourceXPlayer.removeMoney(itemCount)
			targetXPlayer.addMoney(itemCount)
			sourceXPlayer.showNotification('Vous venez de donner ~g~'..itemCount.."$~s~.")
			targetXPlayer.showNotification('Vous avez reçu ~g~'..itemCount.."$~s~.")
		end
	elseif type == "item_account" then
		if itemCount > 0 and sourceXPlayer.getAccount(itemName).money >= itemCount then
			sourceXPlayer.removeAccountMoney(itemName, itemCount)
			targetXPlayer.addAccountMoney(itemName, itemCount)
		end
	elseif type == "item_weapon" then
		if not targetXPlayer.hasWeapon(itemName) then
			sourceXPlayer.removeWeapon(itemName)
			targetXPlayer.addWeapon(itemName, itemCount)
		end
	end
end)

RegisterNetEvent('getgps')
AddEventHandler('getgps', function()
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(_source)
    local getgpsinventory = 0

	for i=1, #xPlayer.inventory, 1 do
		local item = xPlayer.inventory[i]

		if item.name == "gps" then
            getgpsinventory = item.count
		end
	end
    
    if getgpsinventory > 0 then
		TriggerClientEvent('addgps', _source)
    end
end)

----- Vetement -----

ESX.RegisterServerCallback('GetTypeZed', function(source, cb)
	local xPlayer = ESX.GetPlayerFromId(source)
	local masque = {}
	MySQL.Async.fetchAll('SELECT * FROM vetement WHERE identifier = @identifier', {
		['@identifier'] = xPlayer.identifier
	}, function(result) 
		for i = 1, #result, 1 do  
			table.insert(masque, {      
                type      = result[i].type,  
				clothe      = result[i].clothe,
				id      = result[i].id,
				nom      = result[i].nom,
				vie      = result[i].vie,
				onPickup = result[i].onPickup
			})
		end  
		cb(masque) 
	end)  
end)

RegisterNetEvent('InsertVetement')
AddEventHandler('InsertVetement', function(type, name, Nom1, lunettes, Nom2, variation)
  local xPlayer = ESX.GetPlayerFromId(source)
  maskx = {[Nom1]=tonumber(lunettes),[Nom2]=tonumber(variation)} 
	MySQL.Async.execute('INSERT INTO vetement (identifier, type, nom, clothe) VALUES (@identifier, @type, @nom, @clothe)', { 
		['@identifier']   = xPlayer.identifier,
    	['@type']   = type,
    	['@nom']   = name,
    	['@clothe'] = json.encode(maskx)
	})
end)

RegisterNetEvent('InsertTenue')
AddEventHandler('InsertTenue', function(type, name, clothe)
	local xPlayer = ESX.GetPlayerFromId(source)
	MySQL.Async.execute('INSERT INTO vetement (identifier, type, nom, clothe) VALUES (@identifier, @type, @nom, @clothe)',
	{
		['@identifier'] = xPlayer.identifier,
    	['@type'] = type,
    	['@nom'] = name,
    	['@clothe'] = json.encode(clothe)
		}, function(rowsChanged) 
	end)
end)

RegisterNetEvent('DeleteVetement')
AddEventHandler('DeleteVetement', function(supprimer)
    MySQL.Async.execute('DELETE FROM vetement WHERE id = @id', { 
        ['@id'] = supprimer 
    }) 
end)

RegisterNetEvent("marketPaiement")
AddEventHandler("marketPaiement", function(price, itemSelect, itemLabelSelect, quantity)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getMoney() >= price then
        if xPlayer.canCarryItem(itemSelect, quantity) then
            xPlayer.removeMoney(price)
            xPlayer.addInventoryItem(itemSelect, quantity)
            xPlayer.showNotification('Vous avez ~b~effectué~s~ un ~b~paiement~s~ de ~b~'..price..'$~s~ pour '..quantity..' ~b~'..itemLabelSelect..'')  			
        else
            xPlayer.showNotification('Vous n\'avez pas assez de place.')
        end
    else
        xPlayer.showNotification('~r~Vous n\'avez pas assez d\'argent ~g~('..price..'$)')
    end
end)

RegisterNetEvent('VetementPickupOn')
AddEventHandler('VetementPickupOn', function(supprimer)
    MySQL.Async.execute('UPDATE vetement SET onPickup = @onPickup WHERE id = @id', { 
        ['@id'] = supprimer,
		['@onPickup'] = true 
    }) 
end)

RegisterNetEvent('VetementPickupOff')
AddEventHandler('VetementPickupOff', function(supprimer)
    MySQL.Async.execute('UPDATE vetement SET onPickup = @onPickup WHERE id = @id', { 
        ['@id'] = supprimer,
		['@onPickup'] = false 
    }) 
end)


RegisterNetEvent('VetementPickupIdentifier')
AddEventHandler('VetementPickupIdentifier', function(supprimer, identifier)
    MySQL.Async.execute('UPDATE vetement SET identifier = @identifier WHERE id = @id', { 
        ['@id'] = supprimer,
		['@identifier'] = identifier 
    }) 
end)

RegisterNetEvent('ModifNom')
AddEventHandler('ModifNom', function(id, Actif)   
	MySQL.Sync.execute('UPDATE vetement SET nom = @nom WHERE id = @id', {
		['@id'] = id,   
		['@nom'] = Actif        
	})
end)

RegisterNetEvent('Vetement:giveitem')
AddEventHandler('Vetement:giveitem', function(id, personne, name)   
	local xPlayer = ESX.GetPlayerFromId(source)
	local xPlayerT = ESX.GetPlayerFromId(personne)
	MySQL.Sync.execute('UPDATE vetement SET identifier = @identifier WHERE id = @id', {
		['@id'] = id,   
		['@identifier'] = xPlayerT.identifier     
	})
	xPlayerT.showNotification("Vous avez reçu un(e) ~b~"..name)
end)

RegisterNetEvent('Malette')
AddEventHandler('Malette', function(type)
	local xPlayer = ESX.GetPlayerFromId(source)
	local defaultMaxWeight = ESX.GetConfig().MaxWeight
	if type == 1 then
		xPlayer.setMaxWeight(defaultMaxWeight + 2000)
		xPlayer.showNotification("~r~Informations~s~\nVous avez maintenant une capacité en plus de : "..(math.floor(2000/1000)).."KG")
	elseif type == 2 then
		xPlayer.setMaxWeight(defaultMaxWeight + 3000)
		xPlayer.showNotification("~r~Informations~s~\nVous avez maintenant une capacité en plus de : "..(math.floor(3000/1000)).."KG")
	elseif type == 3 then
		xPlayer.setMaxWeight(defaultMaxWeight)
	end
end)