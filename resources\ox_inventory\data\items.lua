return	{
	['money242344'] = {
		label = 'Argentsfwe wfwfefewf',
	},
	['money'] = {
		label = 'Argents',
	},
	--- AMMO BOX
    ['box-ammo-9'] = {
		label = 'Boîte de munition de pistolet',
		weight = 200,
		stack = true
	},

	['box-ammo-45'] = {
		label = 'Boîte de munition de SMG',
		weight = 200,
		stack = true
	},

	['box-ammo-rifle'] = {
		label = 'Boîte de unition de fusil d\'assaut',
		weight = 200,
		stack = true
	},

	['box-ammo-rifle2'] = {
		label = 'Boîte de munition de sniper',
		weight = 200,
		stack = true
	},

	['box-ammo-shotgun'] = {
		label = 'Boîte de munition de fusil à pompe',
		weight = 200,
		stack = true
	},
		['lockpick'] = {
			label = 'Lockpick',
			weight = 0,
		},
		['fish_cooked'] = {
			label = 'Poisson cuit',
			description = "",
			weight = 200,
			allowArmed = false,
			close = false,
			stack = false,
			degrade = 48*60,
			client = {
				status = { hunger = 200000 },
				anim = { dict = 'mp_player_inteat@burger', clip = 'mp_player_int_eat_burger_fp' },
				prop = {
					model = 'prop_cs_steak',
					pos = { x = 0.02, y = 0.02, y = -0.02},
					rot = { x = 0.0, y = 0.0, y = 0.0}
				},
				usetime = 2500,
			}
		},
	["tshirt"] = {
		label = "T-shirt",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	["pants"] = {
		label = "Pantalon",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	["shoes"] = {
		label = "Chaussure",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	["helmet"] = {
		label = "Chapeau",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	["glasses"] = {
		label = "Lunette",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	["mask"] = {
		label = "Mask",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	["bags"] = {
		label = "Sac",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	['parachute'] = {
		label = 'Parachute',
		weight = 8000,
		stack = false,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		}
	},

	["bproof"] = {
		label = "Gilet par balle",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	["watches"] = {
		label = "Montre",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	["bracelets"] = {
		label = "Bracelets",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	["chain"] = {
		label = "Chaine",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},

	["arms"] = {
		label = "Bras",
		weight = 0.05,
		stack = false,
		close = true,
		consume = 0,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},
	["torso"] = {
		label = "Veste",
		weight = 0.05,
		consume = 0,
		stack = false,
		close = true,
		client = {
			anim = { dict = 'clothingshirt', clip = 'try_shirt_positive_d' },
			usetime = 1500
		},
		server= {
			export = 'cfx-clohtingStore.useskin'
		},
		buttons = {
			{
				label = 'Renommer',
				action = function(slot)
					local input = lib.inputDialog('Renommer', {
						{type = 'input', label = '', description = '', required = true, min = 1, max = 7},
					})
					TriggerServerEvent('cfx-clohtingStore:rename', slot, input[1])
				end
			},
		},
	},
	["bread"] = {
		label = "Bread",
		weight = 1,
		stack = true,
		close = true,
	},
	["fixkit"] = {
		label = "Repair Kit",
		weight = 3,
		stack = true,
		close = true,
	},
--weed
	["maleseed"] = {
		label = "MaleSeedItem",
		weight = 2,
		stack = true,
		close = true,
	},
	["fertilizer"] = {
		label = "MaleSeedItem",
		weight = 2,
		stack = true,
		close = true,
	},
	["weed"] = {
		label = "MaleSeedItem",
		weight = 2,
		stack = true,
		close = true,
	},

---brutal ambulance
	["medikit"] = {
		label = "Medikit",
		weight = 2,
		stack = true,
		close = true,
	},
	["bandage"] = {
		label = "Bandage",
		weight = 2,
		stack = true,
		close = true,
	},
	["head_bandage"] = {
		label = "Bandage",
		weight = 2,
		stack = true,
		close = true,
	},
	["arm_wrap"] = {
		label = "Bandage",
		weight = 2,
		stack = true,
		close = true,
	},
	["leg_plaster"] = {
		label = "Bandage",
		weight = 2,
		stack = true,
		close = true,
	},
	["body_bandage"] = {
		label = "Bandage",
		weight = 2,
		stack = true,
		close = true,
	},
	["small_heal"] = {
		label = "Bandage",
		weight = 2,
		stack = true,
		close = true,
	},
	["big_heal"] = {
		label = "Bandage",
		weight = 2,
		stack = true,
		close = true,
	},

	---brutal ambulance
	["phone"] = {
		label = "Téléphone",
		weight = 1,
		stack = true,
		close = true,
	},

	['radio'] = {
		label = 'Radio',
		weight = 100,
		stack = true,
		close = true,
		client = {
			export = 'es_extended',
			remove = function(total)
				-- Disconnets a player from the radio when all his radio items are removed.
				if total < 1 and GetConvar('radio:disconnectWithoutRadio', 'true') == 'true' then
					exports.es_extended:leaveRadio()
				end
			end
		}
	},
	["water"] = {
		label = "Water",
		weight = 1,
		stack = true,
		close = true,
	},
	--- key 
	["key-1"] = {
		label = "Clé (1)",
		weight = 1,
		stack = true,
		close = true,
	},

	["alive_chicken"] = {
		label = "Living chicken",
		weight = 1,
		stack = true,
		close = true,
	},

	["blowpipe"] = {
		label = "Blowtorch",
		weight = 2,
		stack = true,
		close = true,
	},

	["cannabis"] = {
		label = "Cannabis",
		weight = 3,
		stack = true,
		close = true,
	},

	["carokit"] = {
		label = "Body Kit",
		weight = 3,
		stack = true,
		close = true,
	},

	["carotool"] = {
		label = "Tools",
		weight = 2,
		stack = true,
		close = true,
	},

	["clothe"] = {
		label = "Cloth",
		weight = 1,
		stack = true,
		close = true,
	},

	["copper"] = {
		label = "Copper",
		weight = 1,
		stack = true,
		close = true,
	},

	["cutted_wood"] = {
		label = "Cut wood",
		weight = 1,
		stack = true,
		close = true,
	},

	["diamond"] = {
		label = "Diamond",
		weight = 1,
		stack = true,
		close = true,
	},

	["essence"] = {
		label = "Gas",
		weight = 1,
		stack = true,
		close = true,
	},

	["fabric"] = {
		label = "Fabric",
		weight = 1,
		stack = true,
		close = true,
	},

	["fish"] = {
		label = "Fish",
		weight = 1,
		stack = true,
		close = true,
	},

	["fixtool"] = {
		label = "Repair Tools",
		weight = 2,
		stack = true,
		close = true,
	},

	["gazbottle"] = {
		label = "Gas Bottle",
		weight = 2,
		stack = true,
		close = true,
	},

	["gold"] = {
		label = "Gold",
		weight = 1,
		stack = true,
		close = true,
	},

	["iron"] = {
		label = "Iron",
		weight = 1,
		stack = true,
		close = true,
	},

	["marijuana"] = {
		label = "Marijuana",
		weight = 2,
		stack = true,
		close = true,
	},

	["packaged_chicken"] = {
		label = "Chicken fillet",
		weight = 1,
		stack = true,
		close = true,
	},

	["packaged_plank"] = {
		label = "Packaged wood",
		weight = 1,
		stack = true,
		close = true,
	},

	["petrol"] = {
		label = "Oil",
		weight = 1,
		stack = true,
		close = true,
	},

	["petrol_raffin"] = {
		label = "Processed oil",
		weight = 1,
		stack = true,
		close = true,
	},

	["slaughtered_chicken"] = {
		label = "Slaughtered chicken",
		weight = 1,
		stack = true,
		close = true,
	},

	["stone"] = {
		label = "Stone",
		weight = 1,
		stack = true,
		close = true,
	},

	["washed_stone"] = {
		label = "Washed stone",
		weight = 1,
		stack = true,
		close = true,
	},

	["wood"] = {
		label = "Wood",
		weight = 1,
		stack = true,
		close = true,
	},

	["wool"] = {
		label = "Wool",
		weight = 1,
		stack = true,
		close = true,
	},

	["12_ammo"] = {
		label = "Munitions Calibre 12",
		weight = 10,
		stack = true,
		close = true,
	},

	["45acp_ammo"] = {
		label = "45acp",
		weight = 10,
		stack = true,
		close = true,
	},

	["762mm_ammo"] = {
		label = "7.62mm",
		weight = 10,
		stack = true,
		close = true,
	},

	["9mm_ammo"] = {
		label = "9mm",
		weight = 10,
		stack = true,
		close = true,
	},

	["acide"] = {
		label = "Acide sulfurique",
		weight = 1000,
		stack = true,
		close = true,
	},

	["aliment"] = {
		label = "Aliment",
		weight = 200,
		stack = true,
		close = true,
	},

	["alimenttrait"] = {
		label = "Aliment (Traitement)",
		weight = 200,
		stack = true,
		close = true,
	},

	["bmx"] = {
		label = "Bmx",
		weight = 10000,
		stack = true,
		close = true,
	},

	["cafe"] = {
		label = "Café",
		weight = 100,
		stack = true,
		close = true,
	},

	["camera"] = {
		label = "Camera",
		weight = 1000,
		stack = true,
		close = true,
	},

	["cannepeche"] = {
		label = "Canne à pêche",
		weight = 2000,
		stack = true,
		close = true,
	},

	["carrosserie"] = {
		label = "Kit de carrosserie",
		weight = 10000,
		stack = true,
		close = true,
	},

	["cartebancaire"] = {
		label = "Carte Bancaire",
		weight = 100,
		stack = true,
		close = true,
	},

	["cchampagne"] = {
		label = "Champagne (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["ccheddar"] = {
		label = "Cheddar (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["ccoffe"] = {
		label = "Café (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["ccroissant"] = {
		label = "Croissant (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["cdonut"] = {
		label = "Donut (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["cdonuts"] = {
		label = "Donuts (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["cecola"] = {
		label = "E-Cola (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["cfrites"] = {
		label = "Frites",
		weight = 200,
		stack = true,
		close = true,
	},

	["cfromage"] = {
		label = "Fromage (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["champagne"] = {
		label = "Champagne",
		weight = 200,
		stack = true,
		close = true,
	},

	["cheddar"] = {
		label = "Cheddar",
		weight = 100,
		stack = true,
		close = true,
	},

	["cheeseburger"] = {
		label = "Cheeseburger",
		weight = 200,
		stack = true,
		close = true,
	},

	["chiffon"] = {
		label = "Chiffon",
		weight = 100,
		stack = true,
		close = true,
	},

	["chocolat"] = {
		label = "Chocolat",
		weight = 200,
		stack = true,
		close = true,
	},

	["cigarett"] = {
		label = "RedWood",
		weight = 200,
		stack = true,
		close = true,
	},

	["ciseaux"] = {
		label = "Ciseaux",
		weight = 100,
		stack = true,
		close = true,
	},

	["classic_phone"] = {
		label = "Téléphone",
		weight = 500,
		stack = true,
		close = true,
	},

	["cmoteur"] = {
		label = "Moteur (sceller)",
		weight = 20000,
		stack = true,
		close = true,
	},

	["coca"] = {
		label = "Graine de coca",
		weight = 1000,
		stack = true,
		close = true,
	},

	["coffe"] = {
		label = "Café",
		weight = 150,
		stack = true,
		close = true,
	},

	["coke"] = {
		label = "Cocaïne",
		weight = 1000,
		stack = true,
		close = true,
	},

	["cpain"] = {
		label = "Pain Burger (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["cpain_chocolat"] = {
		label = "Pain au Chocolat (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["cpate"] = {
		label = "Pâte à pizza (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["crhum"] = {
		label = "Rhum (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["crochetage"] = {
		label = "Crochet",
		weight = 250,
		stack = true,
		close = true,
	},

	["croissant"] = {
		label = "Croissant",
		weight = 100,
		stack = true,
		close = true,
	},

	["csalade"] = {
		label = "Salade (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["csandwich_thon"] = {
		label = "Sandwich au Thon (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["csprunk"] = {
		label = "Sprunk (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["ctomate"] = {
		label = "Tomate (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["ctortillas"] = {
		label = "Tortillas (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["cviande"] = {
		label = "Viande préparé (sceller)",
		weight = 200,
		stack = true,
		close = true,
	},

	["donut"] = {
		label = "Donut",
		weight = 100,
		stack = true,
		close = true,
	},

	["donuts"] = {
		label = "Donuts",
		weight = 100,
		stack = true,
		close = true,
	},

	["drill"] = {
		label = "Perceuse",
		weight = 5000,
		stack = true,
		close = true,
	},

	["ecola"] = {
		label = "E-Cola",
		weight = 200,
		stack = true,
		close = true,
	},

	["esturgeonpale"] = {
		label = "Esturgeon Pâle",
		weight = 500,
		stack = true,
		close = true,
	},

	["fishingrod"] = {
		label = "Canne à Pêche",
		weight = 2000,
		stack = true,
		close = true,
	},

	["fixter"] = {
		label = "Fixter",
		weight = 1,
		stack = true,
		close = true,
	},

	["flashlight"] = {
		label = "Lampe",
		weight = 500,
		stack = true,
		close = true,
	},

	["frites"] = {
		label = "Frites",
		weight = 100,
		stack = true,
		close = true,
	},

	["fromage"] = {
		label = "Fromage",
		weight = 100,
		stack = true,
		close = true,
	},

	["gps"] = {
		label = "GPS",
		weight = 100,
		stack = true,
		close = true,
	},

	["grainweed"] = {
		label = "Graîne weed",
		weight = 1000,
		stack = true,
		close = true,
	},

	["grandbouche"] = {
		label = "Grand Bouche",
		weight = 500,
		stack = true,
		close = true,
	},

	["handcuff"] = {
		label = "Menotte",
		weight = 100,
		stack = true,
		close = true,
	},

	["hotdog"] = {
		label = "Hotdog",
		weight = 400,
		stack = true,
		close = true,
	},

	["jumelle"] = {
		label = "Jumelle",
		weight = 1000,
		stack = true,
		close = true,
	},

	["jusfruit"] = {
		label = "Jus de fruit",
		weight = 200,
		stack = true,
		close = true,
	},

	["ketchup"] = {
		label = "Ketchup",
		weight = 200,
		stack = true,
		close = true,
	},

	["kokanee"] = {
		label = "Kokanee",
		weight = 500,
		stack = true,
		close = true,
	},

	["meuble"] = {
		label = "Meuble en bois",
		weight = 1000,
		stack = true,
		close = true,
	},

	["micro"] = {
		label = "Micro",
		weight = 500,
		stack = true,
		close = true,
	},

	["microperche"] = {
		label = "Micro Perche",
		weight = 1500,
		stack = true,
		close = true,
	},

	["moteur"] = {
		label = "Moteur",
		weight = 1,
		stack = true,
		close = true,
	},

	["ombrearctique"] = {
		label = "Ombre de l'artique",
		weight = 500,
		stack = true,
		close = true,
	},

	["pain"] = {
		label = "Pain Burger",
		weight = 200,
		stack = true,
		close = true,
	},

	["pain_chocolat"] = {
		label = "Pain au chocolat",
		weight = 200,
		stack = true,
		close = true,
	},

	["panini"] = {
		label = "Panini",
		weight = 200,
		stack = true,
		close = true,
	},

	["paracetamol"] = {
		label = "Paracétamol",
		weight = 200,
		stack = true,
		close = true,
	},

	["parapluie"] = {
		label = "Parapluie",
		weight = 200,
		stack = true,
		close = true,
	},

	["patate"] = {
		label = "Patate",
		weight = 200,
		stack = true,
		close = true,
	},

	["pate"] = {
		label = "Pâte à pizza",
		weight = 100,
		stack = true,
		close = true,
	},

	["pelle"] = {
		label = "Pelle",
		weight = 1000,
		stack = true,
		close = true,
	},

	["percherock"] = {
		label = "Perche Roock",
		weight = 500,
		stack = true,
		close = true,
	},

	["petitebouche"] = {
		label = "Petite Bouche",
		weight = 500,
		stack = true,
		close = true,
	},

	["pizzafromage"] = {
		label = "Pizza 4 Fromages",
		weight = 200,
		stack = true,
		close = true,
	},

	["pizzeriaviande"] = {
		label = "Pizzeria Margherita",
		weight = 200,
		stack = true,
		close = true,
	},

	["planche"] = {
		label = "Planche de bois",
		weight = 1000,
		stack = true,
		close = true,
	},

	["pneu"] = {
		label = "Pneu",
		weight = 1,
		stack = true,
		close = true,
	},

	["pot"] = {
		label = "Pot",
		weight = 500,
		stack = true,
		close = true,
	},

	["red_phosphore"] = {
		label = "Phosphore rouge",
		weight = 1000,
		stack = true,
		close = true,
	},

	["repairkit"] = {
		label = "Moteur",
		weight = 20000,
		stack = true,
		close = true,
	},

	["rhum"] = {
		label = "Rhum",
		weight = 200,
		stack = true,
		close = true,
	},

	["sacpoubelle"] = {
		label = "Sac Poubelle",
		weight = 20000,
		stack = true,
		close = true,
	},

	["salade"] = {
		label = "Salade",
		weight = 200,
		stack = true,
		close = true,
	},

	["sandwich"] = {
		label = "Sandwich",
		weight = 200,
		stack = true,
		close = true,
	},

	["sandwich_poulet"] = {
		label = "Sandwich au poulet",
		weight = 200,
		stack = true,
		close = true,
	},

	["sandwich_thon"] = {
		label = "Sandwich au thon",
		weight = 200,
		stack = true,
		close = true,
	},

	["soda"] = {
		label = "Soda",
		weight = 1,
		stack = true,
		close = true,
	},

	["sodium_hydroxide"] = {
		label = "Methylamine",
		weight = 1000,
		stack = true,
		close = true,
	},

	["sprunk"] = {
		label = "Sprunk",
		weight = 200,
		stack = true,
		close = true,
	},

	["taco"] = {
		label = "Taco",
		weight = 1,
		stack = true,
		close = true,
	},

	["tacos"] = {
		label = "Tacos",
		weight = 200,
		stack = true,
		close = true,
	},

	["tomate"] = {
		label = "Tomate",
		weight = 100,
		stack = true,
		close = true,
	},

	["tortillas"] = {
		label = "Tortillas",
		weight = 100,
		stack = true,
		close = true,
	},

	["truitearc"] = {
		label = "Truite Arc-en-Ciel",
		weight = 1000,
		stack = true,
		close = true,
	},

	["truitebull"] = {
		label = "Truite Bull",
		weight = 500,
		stack = true,
		close = true,
	},

	["truitelac"] = {
		label = "Truite de Lac",
		weight = 500,
		stack = true,
		close = true,
	},

	["viande"] = {
		label = "Viande préparé",
		weight = 100,
		stack = true,
		close = true,
	},

	["viandebiche"] = {
		label = "Viande (Biche)",
		weight = 2000,
		stack = true,
		close = true,
	},

	["viandelapin"] = {
		label = "Viande (Lapin)",
		weight = 500,
		stack = true,
		close = true,
	},

	["viandesanglier"] = {
		label = "Viande (Sanglier)",
		weight = 1000,
		stack = true,
		close = true,
	},

	["WEAPON_BRIEFCASE"] = {
		label = "Malette",
		weight = 1000,
		stack = true,
		close = true,
	},

	["WEAPON_BRIEFCASE_02"] = {
		label = "Malette",
		weight = 1000,
		stack = true,
		close = true,
	},

	["WEAPON_STINGER"] = {
		label = "Stinger",
		weight = 5000,
		stack = true,
		close = true,
	},

	["WEAPON_DIGISCANNER"] = {
		label = "Digiscanner",
		weight = 1000,
		stack = true,
		close = true,
	},

	["WEAPON_PELLE"] = {
		label = "Pelle",
		weight = 1000,
		stack = true,
		close = true,
	},

	["WEAPON_HANDCUFFS"] = {
		label = "Handcuffs",
		weight = 200,
		stack = true,
		close = true,
	},

	["WEAPON_GARBAGEBAG"] = {
		label = "Garbage Bag",
		weight = 1000,
		stack = true,
		close = true,
	},
}