/* Import des polices d'ox_inventory */
@import url(https://fonts.googleapis.com/css?family=Poppins:200,400,500,600,700);
@import url(https://fonts.googleapis.com/css2?family=Open+Sans&display=swap);

/* Variables CSS d'ox_inventory */
:root {
    --font-family: Poppins;
    --font-family-bold: Poppins-Bold;
    --font-family-medium: Poppins-Medium;
    --font-family-regular: Poppins-Regular
}

/* Animations d'ox_inventory */
@keyframes shake {
    0% { transform: translate(1px, 1px) rotate(0deg) }
    10% { transform: translate(-1px, -2px) rotate(-5deg) }
    20% { transform: translate(-3px, 0px) rotate(5deg) }
    30% { transform: translate(3px, 2px) rotate(0deg) }
    40% { transform: translate(1px, -1px) rotate(5deg) }
    50% { transform: translate(-1px, 2px) rotate(-5deg) }
    60% { transform: translate(-3px, 1px) rotate(0deg) }
    70% { transform: translate(3px, 1px) rotate(-5deg) }
    80% { transform: translate(-1px, -1px) rotate(5deg) }
    90% { transform: translate(1px, 2px) rotate(0deg) }
    100% { transform: translate(1px, -2px) rotate(-5deg) }
}

@keyframes openmenu {
    0% { opacity: 0 }
    to { opacity: 100% }
}

body {
    margin: 0;
    font-family: var(--font-family);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    height: 100vh;
    background: 0 !important;
    overflow: hidden !important;
    user-select: none;
    position: fixed;
}

* {
    padding: 0;
    margin: 0;
    color: #fff;
    font-family: var(--font-family);
}

.ui {
    position: relative;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(44, 44, 44, 0.486);
    opacity: 1;
    display: block;
    z-index: 0;
    animation-name: openmenu;
    animation-duration: .6s;
}


.buttoniconidcard{
    position: absolute;
    top:14vh;
    right:71.5vh;
}

.buttoniconidcard *{
    display: flex;
    opacity: 0.7;
}
.buttoniconidcard .raccours3:hover{
    opacity: 0.2;
}
.buttoniconidcard img{
    width: 40%;
    height: 40%;
    right: 1vh;
}
.buttoniconidcard .raccours3:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.buttoniconidcard > .raccours3 > span{
    position: relative;
    right:1.9vh;
    font-size: 2vh;
}


.raccours3 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.raccours3:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.raccours3:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.raccours3 *{
  transform: rotate(90deg);
}
.raccours3:before {
    bottom: 0;
    height: 50%;
}
.raccours3 i{
    position: absolute;
    font-size: 4vh;
}


.raccourci2 {
    position: relative;
    top: 17.5vh;
    left: 6vh;
    display: table;
}
.raccours10,
.raccours11 {
    font-weight: bold;
    font-size: 3.3vh;
}

.autourname{
    font-weight: bold;
    font-size: 3.3vh;
}
.raccours10:hover {
    opacity: 0.7;
}
.autourname:hover {
    opacity: 0.7;
}

.buttoniconbag{
    position: absolute;
    top:14vh;
    right:93.5vh;
}

.buttoniconbag *{
    display: flex;
    opacity: 0.7;
}
.buttoniconbag .raccours1:hover{
    opacity: 0.2;
}
.buttoniconbag img{
    width: 40%;
    height: 40%;
    right: 1vh;
}
.buttoniconbag .raccours1:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.buttoniconbag > .raccours1 > span{
    position: relative;
    right:1.9vh;
    font-size: 2vh;
}


.raccours1 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.raccours1:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.raccours1:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.raccours1 *{
  transform: rotate(90deg);
}
.raccours1:before {
    bottom: 0;
    height: 50%;
}
.raccours1 i{
    position: absolute;
    font-size: 5vh;
    /*filter: drop-shadow(1px 1px 0 black) drop-shadow(-1px -1px 0 black);*/
}


.buttoniconvtm{
    position: absolute;
    top:14vh;
    right:82.5vh;
}

.buttoniconvtm *{
    display: flex;
    opacity: 0.7;
}
.buttoniconvtm .raccours2:hover{
    opacity: 0.2;
}
.buttoniconvtm img{
    width: 40%;
    height: 40%;
    right: 1vh;
}
.buttoniconvtm .raccours2:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.buttoniconvtm > .raccours2 > span{
    position: relative;
    right:1.9vh;
    font-size: 2vh;
}


.raccours2 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.raccours2:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.raccours2:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.raccours2 *{
  transform: rotate(90deg);
}
.raccours2:before {
    bottom: 0;
    height: 50%;
}
.raccours2 i{
    position: absolute;
    font-size: 5vh;
    /*filter: drop-shadow(1px 1px 0 black) drop-shadow(-1px -1px 0 black);*/
}


.inventory {
    position: absolute;
    left: 50vw;
    top: 50vh;
    transform: translate(-50%, -50%);
    width: 93.5vw;
    height: 60vh;
    color: rgba(255, 255, 255, 0.158);
    border-radius: 2vh;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 40px;
}

/* Styles de grille d'inventaire inspirés d'ox_inventory */
#playerInventory {
    display: grid;
    width: 530px;
    height: calc(64vh + 96px);
    grid-template-columns: repeat(5, 98px);
    grid-auto-rows: 95px;
    gap: 5px;
    overflow-y: scroll;
    margin-top: 10px;
}

/* Scrollbar personnalisée comme ox_inventory */
#playerInventory::-webkit-scrollbar {
    width: 5px;
}

#playerInventory::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.4);
    border-radius: 10px;
}

#playerInventory::-webkit-scrollbar-thumb {
    background: #5c1c88;
    border-radius: 10px;
}

/* Hotbar inspiré d'ox_inventory */
#playerInventoryFastItems {
    display: flex;
    align-items: center;
    gap: 2px;
    justify-content: center;
    width: 100%;
    position: absolute;
    bottom: 2vh;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
}

/* Styles des slots de hotbar */
.hotbar-item-slot {
    width: 10.2vh;
    height: 10.2vh;
    background-color: rgba(0, 0, 0, .4);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 45px;
    color: #fff;
    border-radius: 2px;
    transition: all 0.2s ease;
    cursor: pointer;
}

#otherInventory {
    display: grid;
    width: 530px;
    height: calc(64vh + 96px);
    grid-template-columns: repeat(5, 98px);
    grid-auto-rows: 95px;
    gap: 5px;
    overflow-y: scroll;
    margin-top: 10px;
}

/* Scrollbar pour l'autre inventaire */
#otherInventory::-webkit-scrollbar {
    width: 5px;
}

#otherInventory::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.4);
    border-radius: 10px;
}

#otherInventory::-webkit-scrollbar-thumb {
    background: #5c1c88;
    border-radius: 10px;
}

#count {
    border: none;
    outline: 0;
    font-size: 1.8vh;
}

input::-webkit-inner-spin-button,
input::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0
}

input[type=number] {
    -moz-appearance: textfield;
    appearance: textfield;
}

/* Styles des slots inspirés d'ox_inventory */
.slot {
    background-color: rgba(0, 0, 0, .4);
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    background-size: 45px;
    color: #fff;
    border-radius: 2px;
    width: 98px;
    height: 95px;
    border: none;
    margin: 0;
    transition: all 0.2s ease;
    cursor: pointer;
}

/* Animation de drag pour les items */
.ui-draggable-dragging {
    transform: rotate(5deg) scale(1.1);
    z-index: 1000;
    opacity: 0.8;
    animation: shake 0.5s infinite;
}

.slotfix {
    background-color: rgba(0, 0, 0, .4);
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    background-size: 45px;
    color: #fff;
    border-radius: 2px;
    width: 98px;
    height: 95px;
    border: none;
    margin: 0;
    transition: all 0.2s ease;
}

.slot:hover {
    background-color: rgba(0, 0, 0, .6);
    transform: scale(1.02);
}

.slotfix:hover {
    background-color: rgba(0, 0, 0, .6);
    transform: scale(1.02);
}

.item-box2 {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.item-box2:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.item-box2:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.item-box2 *{
  transform: rotate(90deg);
}
.item-box2:before {
    bottom: 0;
    height: 50%;
}
.item-box2 i{
    position: absolute;
    font-size: 4vh;

}
.cloth-items2{
    position: absolute;
    top:29vh;
    right:35vw;
}

.cloth-items2 *{
    display: flex;
    opacity: 0.7;
}
.cloth-items2 .item-box2:hover{
    opacity: 0.2;
}
.cloth-items2 img{
    width: 70%;
    height: 70%;
    right: 1vh;
}
.cloth-items2 .item-box2:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.cloth-items2 > .item-box2 > span{
    position: relative;
    right:1.9vh;
    font-size: 1.8vh;
}

.item-box {
    display: inline-flex;
    width: 8vh;
    height: 8vh;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    border-radius: 3%;
    transform: rotate(-90deg);
    margin-top:1vh;
    margin-right:0.5vw;
    justify-content: center;
    place-items: center;
}
.item-box:before{
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
}
.item-box:after {
    content: "";
    height: 20%;
    position: absolute;
    right: 0;
    top:0;
}
.item-box *{
  transform: rotate(90deg);
}
.item-box:before {
    bottom: 0;
    height: 50%;
}
.item-box i{
    position: absolute;
    font-size: 4vh;
    /*filter: drop-shadow(1px 1px 0 black) drop-shadow(-1px -1px 0 black);*/
}
.cloth-items{
    position: absolute;
    top:29vh;
    right:57.5vw;
}

.cloth-items *{
    display: flex;
    opacity: 0.7;
}
.cloth-items .item-box:hover{
    opacity: 0.2;
}

.cloth-items img{
    width: 70%;
    height: 70%;
    right: 1vh;
}

.cloth-items .item-box:nth-child(9){
    position: absolute;
    right:-10vh;
    bottom:0vh;
}
.cloth-items > .item-box > span{
    position: relative;
    right:1.9vh;
    font-size: 1.8vh;
}

.slotFast {
    float: left;
    width: 9.2vh;
    height: 9.2vh;
    bottom: 250%;
    left: 64vh;
    color: #fff;
    border: 1.5px solid rgba(255,255,255,0.2);
    background-color: rgba(17,17,17,0.319);
    margin: 0.5vh;
    border-radius: 3%;
    position: relative
}

.slotFast:hover {
    opacity: 0.8;
}

/* Styles des items inspirés d'ox_inventory */
.item,
.item-other {
    width: 100%;
    height: 100%;
    background-size: 45px;
    background-repeat: no-repeat;
    background-position: center center;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: 0;
}

.item-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, .4);
    color: #fff;
    text-align: center;
    font-family: var(--font-family);
    font-size: 9px;
    padding: 2px 3px;
    border-radius: 0;
    text-transform: none;
    overflow: hidden;
    text-overflow: ellipsis;
    z-index: 500;
    margin: 0;
    width: auto;
    min-height: auto;
}

/* Numéro de slot comme ox_inventory */
.slot-number {
    background-color: #5c1c88;
    color: #fff;
    height: 17px;
    border-radius: 0px 0px 0px 3px;
    width: 16px;
    font-weight: 400;
    padding: 3px;
    font-size: 12px;
    font-family: var(--font-family);
    position: absolute;
    right: 0;
    top: 0;
    text-align: center;
}

.ui-draggable-dragging .item-count,
.ui-draggable-dragging .item-name {
    display: none
}

.item-count {
    position: absolute;
    text-align: left;
    font-family: 'Roboto', sans-serif;
    font-weight: bold;
    width: 2vh;
    margin-top: -0.9vh;
    margin-left: 0vh;
    height: 2vh;
    color: rgb(189, 189, 189);
    z-index: 500;
    font-size: 0.9vh
}

.keybind {
    position: absolute;
    text-align: right;
    width: 11.4vh;
    margin-top: -15px;
    margin-left: -100px;
    height: 20px;
    z-index: 500;
    font-size: 0vh
}

.ammoIcon {
    width: 8px;
    height: 8px
}

.info-div {
    text-align: left;
    padding: 5px;
    width: 174px;
    position: absolute;
    font-size: 1.2vh;
    left: 64%;
    top: 1%;
    transform: translate(-50%, -50%)
}

.info-div2 {
    text-align: left;
    padding: 5px;
    width: 155px;
    position: absolute;
    font-size: 1.2vh;
    left: 30%;
    top: 1%;
    transform: translate(-50%, -50%)
}

/* Styles des contrôles inspirés d'ox_inventory */
.controls-div {
    display: flex;
    flex-direction: row;
    gap: 15px;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 10vh;
    left: 50%;
    transform: translateX(-50%);
}

.control {
    transition: .2s;
    padding: 8px 0;
    font-family: var(--font-family);
    font-size: 18px;
    text-align: center;
    outline: 0;
    border: 0;
    color: #fff;
    background-color: rgba(0, 0, 0, .4);
    width: 6vw;
    border-radius: 4px;
    position: relative;
    margin: 0;
}

.control2 {
    font-size: 14px;
    color: #fff;
    background-color: rgba(0, 0, 0, .3);
    border-radius: 6px;
    border: .5px solid rgba(255, 255, 255, .0823);
    transition: .2s;
    padding: 12px 8px;
    text-transform: uppercase;
    font-family: var(--font-family);
    width: 7vw;
    position: relative;
    margin: 0;
    text-align: center;
    cursor: pointer;
}

.control3 {
    font-size: 14px;
    color: #fff;
    background-color: rgba(0, 0, 0, .3);
    border-radius: 6px;
    border: .5px solid rgba(255, 255, 255, .0823);
    transition: .2s;
    padding: 12px 8px;
    text-transform: uppercase;
    font-family: var(--font-family);
    width: 7vw;
    position: relative;
    margin: 0;
    text-align: center;
    cursor: pointer;
}

.control2:hover,
.control3:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.control2:active,
.control3:active {
    transform: translateY(1px);
}

/* Styles pour les tooltips inspirés d'ox_inventory */
.tooltip-wrapper {
    pointer-events: none;
    display: flex;
    width: 200px;
    padding: 8px;
    flex-direction: column;
    min-width: 200px;
    color: #fff;
    font-family: var(--font-family);
    border-radius: 2px;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 9999;
    position: absolute;
}

.tooltip-wrapper p {
    font-size: 12px;
    font-weight: 400;
    margin: 0;
}

/* Effet de brillance sur les slots */
.slot.highlight {
    box-shadow: 0 0 15px rgba(92, 28, 136, 0.8);
    border: 2px solid #5c1c88;
}

/* Animation de pulse pour les notifications */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.notification-pulse {
    animation: pulse 0.6s ease-in-out;
}


.control.disabled {
    background-color: rgba(36, 36, 36, 0.479);
}

.hoverControl {
    background-color: rgba(36, 36, 36, 0.479);
}

/* .used-item-slot {
    position: absolute;
    right: 50vh;
    bottom: 50vh;
    height: 125px;
    width: 110px;
    background-color: rgba(114, 114, 114, 0.308);
    border-style: solid;
    border-color: rgba(255, 255, 255, .2);
    border-width: 1px 1px 1px 1px
} */

/* #used-item-image {
    height: 12.5vh;
    width: 11vh;
    background-image: url(img/used-item.png);
    background-repeat: no-repeat;
    background-size: 0%;
    background-position-x: 0
}


#added-item-image {
    height: 125px;
    width: 110px;
    background-image: url(img/added-item.png);
    background-repeat: no-repeat;
    background-size: 80%;
    background-position-x: 0
} */
/* 
.item-name-bg {
    width: 100%;
    width: 117px;
    height: 20px;
    position: absolute;
    bottom: 0
} */

#otherInventory::-webkit-scrollbar-track,
#playerInventory::-webkit-scrollbar-track {
    background-color: none;
    border: none
}

#controlstrunk {
    width: 35vh;
    height: 50vh;
    float: left;
    position: relative;
    left: 62vh;
    top: -1.5vw;
}


.weighttrunk-div {
    text-align: left;
    padding: 0.5vh;
    width: 15.5vh;   
    position: absolute;
    font-size: 1.3vh;
    left: 22vw;
    top: 7%;
    transform: translate(-50%, -50%)
}


#otherInventory::-webkit-scrollbar,
#playerInventory::-webkit-scrollbar {
    width: 1vh
}

.nearbyPlayerButton {
    width: 100vh;
    margin-top: 0.5vh;
    display: block;
    text-decoration: none;
    padding: 0.2vh;
    color: rgba(255, 255, 255, .85);
    background-color: rgba(49, 49, 49, 0.274);
    border-radius: 9vh;
    text-shadow: none;
    font-size: 1.4vh!important;
    outline: 0;
    text-transform: none;
    text-align: center;
    line-height: 3vh;
    border: none
}

.nearbyPlayerButton:hover {
    background-color: rgba(36, 36, 36, 0.2)
}

#noSecondInventoryMessage {
    width: 61.5vh;
    height: 58vh;
    line-height: 58vh;
    text-align: center
}
/* 
@media (max-width: 100vh) {
    .inventory {
        width: 14vh;
    }

    #playerInventory {
        width: 600px;
        height: 500px;
        bottom: 105px;
    }

    #controls {
        width: 200px;
    }

    .control {
        width: 190px;
        height: 40px;
        left: 500px;
    }

    .raccours {
        top: 14%;
        right: -2%;
    }

    .info-div {
        padding: 15px;
        width: 174px;
        left: 105%;
        top: 0.5%;
    }

    .slotFast {
        width: 120px;
        height: 120px;
        bottom: 450%;
    }

    #otherInventory {
        width: 600px;
        height: 500px;
    }

    #noSecondInventoryMessage {
        width: 540px;
    }
} */