local PlayerData = {}

Citizen.CreateThread(function()
	while ESX.GetPlayerData().job == nil do
		Citizen.Wait(10)
	end
    PlayerData = ESX.GetPlayerData()
end)

RegisterNetEvent('esx:setJob') 
AddEventHandler('esx:setJob', function(job)   
    PlayerData.job = job
end)

function refreshPlayerInventory()
    setPlayerInventoryData()
end

RegisterNetEvent("esx_inventoryhud:openPlayerInventory")
AddEventHandler("esx_inventoryhud:openPlayerInventory", function(target, playerName)
	targetPlayer = target
    targetPlayerName = playerName
    setPlayerInventoryData()
    openPlayerInventory()
end)

local openInventoryPlayer = true

RegisterCommand('fouiller', function()
    local closestPlayer = GetNearbyPlayer(true)

    if closestPlayer then
        if IsEntityPlayingAnim(GetPlayerPed(closestPlayer), 'random@mugging3', 'handsup_standing_base', 3) or IsPedCuffed(GetPlayerPed(closestPlayer)) or IsPedRagdoll(GetPlayerPed(closestPlayer)) then
            ExecuteCommand('me fouille quelqu\'un')
            if openInventoryPlayer then
                openInventoryPlayer = false
                TriggerEvent("esx_inventoryhud:openPlayerInventory", GetPlayerServerId(closestPlayer), GetPlayerName(PlayerPedId()))
                ESX.Streaming.RequestAnimDict('missexile3', function()
                    TaskPlayAnim(PlayerPedId(), 'missexile3', 'ex03_dingy_search_case_a_michael', 8.0, -8.0, -1, 35, 0.0, false, false, false)
                end)
                while not openInventoryPlayer do
                    if IsControlJustReleased(0, 37) then
                        openInventoryPlayer = true
                        ClearPedTasks(PlayerPedId())
                    end
                    Wait(5)
                end
            end
        else
            ESX.Notification("~r~Impossible l'individu doit lever les mains ou être menotté.")
        end
    end
end)

function setPlayerInventoryData()
    ESX.TriggerServerCallback("esx_inventoryhud:getPlayerInventory", function(data)
        items = {}
        inventory = data.inventory
        accounts = data.accounts
        money = data.money
        weapons = data.weapons
        cards = data.cards
        idcard = data.idcard
        kevlar = data.kevlar

        if money ~= nil and money > 0 then
            moneyData = {
                label = "Espèces",
                name = "cash",
                type = "item_money",
                count = money,
                usable = false,
                rare = false,
                limit = -1,
                canRemove = true
            }

            table.insert(items, moneyData)
        end

        if accounts ~= nil then
            for key, value in pairs(accounts) do
                if not shouldSkipAccount(accounts[key].name) then
                    local canDrop = accounts[key].name ~= "bank"

                    if accounts[key].money > 0 then
                        accountData = { 
                            label = "Espèces Inconnu",
                            count = accounts[key].money,
                            type = "item_account",
                            name = accounts[key].name,
                            usable = false,
                            rare = false,
                            weight = 0,
                            canRemove = canDrop
                        }
                        table.insert(items, accountData)
                    end
                end
            end
        end

        if inventory ~= nil then
            for k, v in pairs(inventory) do
                if v.count > 0 then
                    itemData = {
                        label = v.label,
                        name = v.name,
                        type = "item_standard",
                        count = v.count,
                        usable = false,
                        rename = false,
                        rare = false,
                        weight = -1
                    }
                    table.insert(items, itemData)
                end
            end
        end

        if kevlar ~= nil then
            for k, v in pairs(kevlar) do
                if v.type == "kevlar" then
                    cardsData = {
                        label = v.nom,
                        name = "kevlar",
                        type = "item_kev",
                        id = v.id,
                        count = 1,
                        usable = false,
                        rename = false,
                        rare = false,
                        weight = -1
                    }
                    table.insert(items, cardsData)
                end
            end
        end

        SendNUIMessage(
            {
                action = "setSecondInventoryItems",
                itemList = items
            }
        )
    end, targetPlayer)
end

function openPlayerInventory()
    loadPlayerInventory(currentMenu)
    isInInventory = true

    SendNUIMessage(
        {
            action = "display",
            type = "player"
        }
    )

    DisplayRadar(false)
    SetNuiFocus(true, true)
    SetKeepInputMode(true)
end

local Perm = {}
Perm.PlyGroup = nil
Citizen.CreateThread(function()
    while Perm.PlyGroup == nil do
        Wait(1000)
        ESX.TriggerServerCallback('GetGroup', function(group) 
            Perm.PlyGroup = group 
        end)
		Citizen.Wait(10)
	end
end)


RegisterNUICallback("PutIntoPlayer", function(data, cb)
    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
    if closestPlayer ~= -1 and closestDistance <= 3.0 then
        if GetPlayerServerId(closestPlayer) == targetPlayer then
            if type(data.number) == "number" and math.floor(data.number) == data.number then
                local count = tonumber(data.number)
                if data.item.type == "item_weapon" then
                    count = data.item.count
                end
                TriggerServerEvent("esx_inventoryhud:tradePlayerItem", GetPlayerServerId(PlayerId()), targetPlayer, data.item.type, data.item.name, count, data.item)
            end
            Wait(250)
            refreshPlayerInventory()
            loadPlayerInventory(currentMenu)
        end
    end
    cb("ok")
end)

RegisterNUICallback("TakeFromPlayer", function(data, cb)
    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()
    if closestPlayer ~= -1 and closestDistance <= 3.0 then
        if GetPlayerServerId(closestPlayer) == targetPlayer then
            if PlayerData.job and PlayerData.job.name == 'police' or Perm.PlyGroup == 'admin' or Perm.PlyGroup == 'moderator' then
                if type(data.number) == "number" and math.floor(data.number) == data.number then
                    local count = tonumber(data.number)
                    if data.item.type == "item_weapon" then
                        count = data.item.count
                    end
                    TriggerServerEvent("esx_inventoryhud:tradePlayerItem", targetPlayer, GetPlayerServerId(PlayerId()), data.item.type, data.item.name, count, data.item)
                end
            end
            Wait(250)
            refreshPlayerInventory()
            loadPlayerInventory(currentMenu)
        end
    end
    cb("ok")
end)


local plyragdoll = false

RegisterCommand('+ragdoll', function()
    if not IsPedFalling(GetPlayerPed(-1)) then
        plyragdoll = not plyragdoll
    end
end)

RegisterKeyMapping('+ragdoll', 'S\'endormir / se réveiller', 'keyboard', 'j')

Citizen.CreateThread(function()
    while true do
        local time = 500
        local plyPed = PlayerPedId()
        local entityAlpha = GetEntityAlpha(GetPlayerPed(-1))

        if not IsPedSittingInAnyVehicle(plyPed) then
            if entityAlpha < 100 then
               time = 0
            else
                if plyragdoll and IsControlJustReleased(1, 22) or plyragdoll and IsControlJustReleased(1, 51) then
                    plyragdoll = not plyragdoll
                end
                if plyragdoll then
                    time = 0
                    SetPedRagdollForceFall(plyPed)
                    ResetPedRagdollTimer(PlayerPedId())
                    SetPedToRagdoll(PlayerPedId(), 1000, 1000, 3, 0, 0, 0)
                    ResetPedRagdollTimer(PlayerPedId())
                    DisplayNotification("Appuyez sur ~INPUT_CONTEXT~ ou ~INPUT_JUMP~ pour ~b~vous relever~w~.")
                end
            end
        end 
        Wait(time)   
    end
end)


function SeTrainerAuSol()
    CreateThread(function()
        if IsPedInAnyVehicle(playerPed, false) then 
            return 
        end
        local PlayerPed = GetPlayerPed(-1)
        coucher = not coucher
        if not coucher then
            Wait(0)
            FreezeEntityPosition(PlayerPed, false)
            local dict, anim = "get_up@directional@transition@prone_to_knees@crawl", "front"
            TaskPlayAnim(PlayerPed, dict, anim, 8.0, -4.0, -1, 9, 0.0)
        else
        if IsPedRunning(PlayerPed)or IsPedSprinting(PlayerPed) or IsPedStrafing(PlayerPed)then
            local dict, anim = "move_jump", "dive_start_run"
            ESX.Streaming.RequestAnimDict(dict)
            TaskPlayAnim(PlayerPed, dict, anim, 8.0, -4.0, -1, 9, 0.0)
            Citizen.Wait(1200)
        end
        CreateThread(function()
            while coucher do 
                Wait(0)
                FreezeEntityPosition(GetPlayerPed(-1), false)
                    if IsPedSwimming(GetPlayerPed(-1)) or IsPedFalling(GetPlayerPed(-1)) then 
                        coucher = false
                        FreezeEntityPosition(GetPlayerPed(-1), false)
                        local dict, anim = "get_up@directional@transition@prone_to_knees@crawl", "front"
                        TaskPlayAnim(GetPlayerPed(-1), dict, anim, 8.0, -4.0, -1, 9, 0.0)
                        break 
                    end
                    if IsControlPressed(1,32) and not IsEntityPlayingAnim(PlayerPed,"move_crawl", "onfront_fwd",3) then
                        local dict, anim = "move_crawl", "onfront_fwd"
                        ESX.Streaming.RequestAnimDict(dict)
                        TaskPlayAnim(PlayerPed, dict, anim, 8.0, -4.0, -1, 9, 0.0)
                    elseif IsControlPressed(1, 33) and not IsEntityPlayingAnim(PlayerPed, "move_crawl", "onfront_bwd", 3) then
                        local dict, anim = "move_crawl", "onfront_bwd"
                        ESX.Streaming.RequestAnimDict(dict)
                        TaskPlayAnim(PlayerPed, dict, anim, 8.0, -4.0, -1, 9, 0.0)
                    end
                    if IsControlPressed(1, 34) then
                        SetEntityHeading(PlayerPed, GetEntityHeading(PlayerPed)+1.0)
                    elseif IsControlPressed(1, 35)then
                        SetEntityHeading(PlayerPed, GetEntityHeading(PlayerPed)-1.0)
                    end
                    if IsControlReleased(1, 32) and IsControlReleased(1, 33) then
                        local dict, anim = "move_crawl", "onfront_fwd"
                        ESX.Streaming.RequestAnimDict(dict)
                        TaskPlayAnim(PlayerPed, dict, anim, 8.0, -4.0, -1, 9, 0.0)
                        FreezeEntityPosition(PlayerPed, true)
                    end
                end 
            end)
        end 
    end)
end

RegisterKeyMapping("allonger","S'allonger au Sol","keyboard","L")

RegisterCommand("allonger", function()
    if not IsPedInAnyVehicle(GetPlayerPed(-1), false) then
		if not IsPedSwimming(GetPlayerPed(-1)) then
			if not IsEntityInWater(GetPlayerPed(-1)) then
				if not IsPedFalling(GetPlayerPed(-1)) then 
        			SeTrainerAuSol()
				end
			end
		end
    end
end)

RegisterKeyMapping("stream","Filmer Rockstar Editor","keyboard","F3")

RegisterCommand("stream",function()
    if IsRecording()then 
        return
        StopRecordingAndSaveClip()
    end
    StartRecording(1)
end)

RegisterCommand("rockstar",function()
	NetworkSessionLeaveSinglePlayer()
	ActivateRockstarEditor()
end)


RegisterKeyMapping("conducteur", "Place Conducteur", "keyboard", "1")
RegisterKeyMapping("passager", "Place Passager", "keyboard", "2")
RegisterKeyMapping("arrieregauche", "Place Arrière Gauche", "keyboard", "3")
RegisterKeyMapping("arrieredroite", "Place Arrière Droite", "keyboard", "4")

RegisterCommand("conducteur", function() 
    local plyPed = GetPlayerPed(-1)
    local plyVehicle = GetVehiclePedIsIn(plyPed, false)
    local CarSpeed = GetEntitySpeed(plyVehicle) * 2.2369
    if IsPedSittingInAnyVehicle(plyPed) then
        if CarSpeed <= 20.0 then
            SetPedIntoVehicle(plyPed, plyVehicle, -1)
        end
    end
end)

RegisterCommand("passager", function()
    local plyPed = GetPlayerPed(-1)
    local plyVehicle = GetVehiclePedIsIn(plyPed, false)
    local CarSpeed = GetEntitySpeed(plyVehicle) * 2.2369
    if IsPedSittingInAnyVehicle(plyPed) then
        if CarSpeed <= 20.0 then
            SetPedIntoVehicle(plyPed, plyVehicle, 0)
        end
    end
end)

RegisterCommand("arrieregauche", function() 
    local plyPed = GetPlayerPed(-1)
    local plyVehicle = GetVehiclePedIsIn(plyPed, false)
    local CarSpeed = GetEntitySpeed(plyVehicle) * 2.2369
    if IsPedSittingInAnyVehicle(plyPed) then
        if CarSpeed <= 20.0 then
            SetPedIntoVehicle(plyPed, plyVehicle, 1)
        end
    end
end)

RegisterCommand("arrieredroite", function() 
    local plyPed = GetPlayerPed(-1)
    local plyVehicle = GetVehiclePedIsIn(plyPed, false)
    local CarSpeed = GetEntitySpeed(plyVehicle) * 2.2369
    if IsPedSittingInAnyVehicle(plyPed) then
        if CarSpeed <= 20.0 then
            SetPedIntoVehicle(plyPed, plyVehicle, 2)
        end
    end
end)

RegisterKeyMapping("+handsup", "Lever les mains", "keyboard", "Y")

RegisterCommand('+handsup', function()
    if not IsPedInAnyVehicle(GetPlayerPed(-1), false) then
        while not HasAnimDictLoaded("random@mugging3") do
            RequestAnimDict("random@mugging3")
            Citizen.Wait(5)
        end
        if not IsEntityPlayingAnim(PlayerPedId(), 'random@mugging3', 'handsup_standing_base', 3) then
            TaskPlayAnim(PlayerPedId(), 'random@mugging3', 'handsup_standing_base', 3.0, 1.0, -1, 50, 0, false, false, false)
        else
            StopAnimTask(PlayerPedId(), 'random@mugging3', 'handsup_standing_base', -3.0)
        end
    end
end)

local crouched = false

RegisterKeyMapping('accroupie', "S'accroupir", 'keyboard', 'X')

RegisterCommand('accroupie', function()
    local plyPed = PlayerPedId()
    
    if not IsPedInAnyVehicle(GetPlayerPed(-1), false) and DoesEntityExist(plyPed) and not IsEntityDead(plyPed) then 
        DisableControlAction(1, 104, true)
        if not IsPauseMenuActive() then 
            RequestAnimSet("move_ped_crouched")
            while not HasAnimSetLoaded("move_ped_crouched") do 
                Citizen.Wait(100)
            end 
            if crouched == true then 
                ResetPedMovementClipset(plyPed, 1.2)
                crouched = false
            elseif crouched == false then
                SetPedMovementClipset(plyPed, "move_ped_crouched", 1.2)
                crouched = true 
            end 
        end
    end 
end)



local fouilleinteract = {   
    {
        name = 'fouille',
        icon = 'fa-solid fa-people-robbery',
        label = 'Fouiller',
        canInteract = function(entity, distance, coords, name, bone)
            return nil == nil
        end,
        onSelect = function()
            ExecuteCommand("fouiller")
        end,
    },
}

exports.ox_target:addGlobalPlayer(fouilleinteract)