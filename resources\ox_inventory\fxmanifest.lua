fx_version 'cerulean'
use_experimental_fxv2_oal 'yes'
lua54 'yes'
game 'gta5'
name 'ox_inventory'
author 'Overextended'
version '2.39.1'
repository 'https://github.com/overextended/ox_inventory'
description 'Slot-based inventory with item metadata support'

dependencies {
    '/server:6116',
    '/onesync',
    'oxmysql',
    'ox_lib',
}

shared_script '@ox_lib/init.lua'

ox_libs {
    'locale',
    'table',
    'math',
}
client_scripts {
  "@es_extended/locale.lua",
  "cl_esx.lua",
  'config.lua',
  'peds.lua',
  'init.lua',
  "RageUI/RMenu.lua",
  "RageUI/menu/RageUI.lua",
  "RageUI/menu/Menu.lua",
  "RageUI/menu/MenuController.lua",
  "RageUI/components/*.lua",
  "RageUI/menu/elements/*.lua",
  "RageUI/menu/items/*.lua",
  "RageUI/menu/panels/*.lua",
  "RageUI/menu/windows/*.lua",
  "clothshop/cl_clothshop.lua",
  "clothvariation/Variations.lua",
  "clothvariation/Functions.lua",
  "clothvariation/Clothing.lua",
}

server_scripts {
    "@es_extended/locale.lua",
    "sv_esx.lua",
    '@oxmysql/lib/MySQL.lua',
    'init.lua',
    'config.lua',
    'clothshop/sv_clothshop.lua',
}


ui_page 'web/build/index.html'

files {
    'client.lua',
    'server.lua',
    'locales/*.json',
    'web/build/index.html',
    'web/build/assets/*.js',
    'web/build/assets/*.css',
    'web/images/*.png',
    'modules/**/shared.lua',
    'modules/**/client.lua',
    'modules/bridge/**/client.lua',
    'data/*.lua',
    'web/**',
}

escrow_ignore {
    '**'
}
