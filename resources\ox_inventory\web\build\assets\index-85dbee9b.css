@import url(https://fonts.googleapis.com/css?family=Poppins:200,400,500,600,700);
@import url(https://fonts.googleapis.com/css2?family=Open+Sans&display=swap);

@font-face {
    font-family: robotocondensedbold;
    src: url(./RobotoCondensed-Bold-9bc003d6.ttf)
}

@import url(https://fonts.googleapis.com/css?family=Poppins:200,400,500,600,700);

@font-face {
    font-family: robotocondensedmedium;
    src: url(./RobotoCondensed-Medium-271c76f9.ttf)
}

@font-face {
    font-family: robotocondensedlight;
    src: url(./RobotoCondensed-Light-13c2f480.ttf)
}

:root {
    --font-family: Poppins;
    --font-family-bold: Poppins-Bold;
    --font-family-medium: Poppins-Medium;
    --font-family-regular: Poppins-Regular
}



@keyframes shake {
    0% {
        transform: translate(1px, 1px) rotate(0deg)
    }

    10% {
        transform: translate(-1px, -2px) rotate(-5deg)
    }

    20% {
        transform: translate(-3px, 0px) rotate(5deg)
    }

    30% {
        transform: translate(3px, 2px) rotate(0deg)
    }

    40% {
        transform: translate(1px, -1px) rotate(5deg)
    }

    50% {
        transform: translate(-1px, 2px) rotate(-5deg)
    }

    60% {
        transform: translate(-3px, 1px) rotate(0deg)
    }

    70% {
        transform: translate(3px, 1px) rotate(-5deg)
    }

    80% {
        transform: translate(-1px, -1px) rotate(5deg)
    }

    90% {
        transform: translate(1px, 2px) rotate(0deg)
    }

    100% {
        transform: translate(1px, -2px) rotate(-5deg)
    }
}

body {
    margin: 0;
    font-family: robotocondensedlight;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    height: 100vh;
    background: 0 !important;
    overflow: hidden !important;
    user-select: none
    
}

@keyframes openmenu {
    0% {
        opacity: 0
    }

    to {
        opacity: 100%
    }
}

#root {
    height: 100%
}

code {
    font-family: source-code-pro, menlo, monaco, consolas, courier new, monospace
}

::-webkit-scrollbar {
    width: 5px
}

.inventory-grid-container-user::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.4);
    border-radius: 10px
}

.inventory-grid-container-user::-webkit-scrollbar-thumb {
    background: #5c1c88;
    border-radius: 10px
}

p {
    margin: 0;
    padding: 0;
    font-family: gilroy-regular, sans-serif
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.app-wrapper {
    height: 100%;
    width: 100%;
    color: #fff
}

.context-menu-list {
    min-width: 100px;
    padding: 10px;
    background-color: rgba(0, 0, 0, .6);
    border-radius: 3px;
    outline: 0;
    display: flex;
    gap: 10px;
    flex-direction: column
}

.context-menu-item {
    height: 40px;
    width: 170px;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, .6);
    outline: 0;
    border: 0;
    color: #fff;
    font-family: var(--font-family);
    justify-content: space-between;
    align-items: center;
    border: solid 1px transparent
}

.context-menu-item:active {
    transform: none
}

.context-menu-item:hover {
    border: solid 1px rgba(200, 200, 200);
    cursor: pointer
}

.tooltip-description {
    padding-top: 5px
}

.tooltip-markdown>p {
    margin: 0
}

button:active {
    transform: translatey(3px)
}

.inventory-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    gap: 40px
}

.item-drag-preview {
    background: rgba(0,0,0,0.4);
    width: 9vh;
    height: 7.5vh;
    z-index: 1;
    position: fixed;
    border-bottom: 15px solid rgba(0,0,0,0.5);
    pointer-events: none;
    top: 0;
    left: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 7vh;
    image-rendering: -webkit-optimize-contrast
}

.inventory-wrapper2 {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    animation-name: openmenu;
    animation-duration: .6s;
    height: 70%
}

.ButtonRow {
    display: flex;
    flex-direction: row;
    gap: 15px
}

.ButtonRow2 {
    display: flex;
    flex-direction: row;
    gap: 15px
}

.inventory-control {
    display: flex;
    align-self: center
}



.inventory-control .inventory-control-wrapper {
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: center;
    align-items: center
}

.inventory-control .inventory-control-input {
    transition: .2s;
    padding: 8px 0;
    font-family: var(--font-family);
    font-size: 18px;
    text-align: center;
    outline: 0;
    border: 0;
    color: #fff;
    background-color: rgba(0, 0, 0, .4);
    width: 6vw;
    border-radius: 4px
}

.inventory-control .inventory-control-button {
    font-size: 14px;
    color: #fff;
    background-color: #0003;
    border-radius: 6px;
    border: .5px solid rgba(255, 255, 255, .0823);
    transition: .2s;
    padding: 12px 8px;
    text-transform: uppercase;
    font-family: var(--font-family);
    width: 7vw
}

.inventory-control-button2 {
    font-size: 14px;
    color: #fff;
    background-color: #0003;
    border-radius: 6px;
    border: .5px solid rgba(255, 255, 255, .0823);
    transition: .2s;
    padding: 15px 8px;
    text-transform: uppercase;
    font-family: var(--font-family);
    width: 10vw;
}

.inventory-control .inventory-control-button:hover {
    background-color: #fff
}

.useful-controls-dialog {
    background-color: #22232c;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #c1c2c5;
    width: 450px;
    display: flex;
    flex-direction: column;
    padding: 16px;
    border-radius: 4px;
    gap: 16px
}

.divider {
    width: 100%;
    height: 1px;
    background-color: #0000
}

.inventory-grid-wrapper {
    display: flex;
    flex-direction: column;
    gap: 7px;
    margin-top: 80px;
    margin-bottom: -5px;
    margin-left: 40vw
}

.inventory-grid-wrapper-user {
    display: flex;
    margin-top: 155px;
    flex-direction: column;
    gap: 5px
}

.inventory-grid-header-wrapper-user {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    padding-bottom: 10px
}

.inventory-grid-header-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: 70px
}

.inventory-grid-container-user {
    display: grid;
    height: calc(64vh + 96px);
    grid-template-columns: repeat(5, 98px);
    grid-auto-rows: 95px;
    gap: 0;
    overflow-y: scroll;
    width: 530px
}

.inventory-grid-container {
    display: grid;
    height: calc(64vh + 96px);
    grid-template-columns: repeat(5, 95px);
    grid-auto-rows: 95px;
    gap: 5px;
    overflow-y: scroll;
    margin-top: 10px
}
.inventory-slot {
    background-color: rgba(0, 0, 0, .4);
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    background-size: 45px;
    color: #fff;
    border-radius: 2px
}


.inventory-slot,
.item-notification-item-box,
.hotbar-item-slot {
    background-color: rgba(0, 0, 0, .4);
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    background-size: 45px;
    color: #fff;
    border-radius: 2px
}


.inventory-slot-clothing {
    background-color: rgba(0, 0, 0, .4);
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    background-size: 10vh;
    color: #fff;
    border-radius: 2px
}

.inventory-slot-label-box {
    background-color: rgba(0, 0, 0, .4);
    color: #fff;
    text-align: center
}

.inventory-slot-label-box-no {
    display: none
}

.inventory-slot-label-text {
    text-transform: none;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 2px 3px;
    font-family: var(--font-family);
    font-size: 9px
}
.inventory-slot-number {
    background-color: #5c1c88;
    color: #fff;
    height: 17px;
    border-radius: 0px 0px 0px 3px;
    width: 16px;
    font-weight: 400;
    padding: 3px;
    font-size: 12px;
    font-family: var(--font-family);
    left: 78px;
    text-align: center;
    top: 0;
    position: absolute
}


.custom-slot-color {
    background-color: transparent;
    border-top: 4px solid #5c1c88;
}


.item-slot-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%
}

.item-slot-wrapper p {
    font-size: 12px
}

.item-slot-header-wrapper,
.item-hotslot-header-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: start
}

.item-hotslot-header-wrapper {
    justify-content: space-between !important
}

.item-slot-info-wrapper {
    color: #fff;
    display: flex;
    margin-top: -2px;
    flex-direction: row;
    padding: 5px;
    gap: 3px;
    
}
.item-slot-info-wrapper p {
    font-family: var(--font-family);
    font-size: 10px
}

.item-slot-currency-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding-right: 3px
}

.item-slot-currency-wrapper p {
    font-size: 14px
}

.item-slot-price-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    padding-right: 3px
}

.item-slot-price-wrapper p {
    font-size: 14px;
    font-family: var(--font-family)
}

.tooltip-wrapper {
    pointer-events: none;
    display: flex;
    width: 200px;
    padding: 8px;
    flex-direction: column;
    min-width: 200px;
    color: #0000;
    font-family: var(--font-family);
    border-radius: 2px;
    background: rgba(0, 0, 0, 0)
}

.tooltip-wrapper p {
    font-size: 12px;
    font-weight: 400
}

.tooltip-header-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center
}

.tooltip-header-wrapper p {
    font-size: 15px;
    font-weight: 400
}

.tooltip-crafting-duration {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center
}

.tooltip-crafting-duration svg {
    padding-right: 3px
}

.tooltip-crafting-duration p {
    font-size: 14px
}

.tooltip-ingredients {
    padding-top: 5px
}

.tooltip-ingredient {
    display: flex;
    flex-direction: row;
    align-items: center
}

.tooltip-ingredient img {
    width: 48px;
    height: 8px;
    padding-right: 5px
}

.hotbar-container {
    display: flex;
    align-items: center;
    gap: 2px;
    justify-content: center;
    width: 100%;
    position: absolute;
    bottom: 2vh
}

.hotbar-item-slot {
    width: 10.2vh;
    height: 10.2vh
}

.hotbar-slot-header-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between
}

.item-notification-container {
    display: flex;
    overflow-x: scroll;
    flex-wrap: nowrap;
    gap: 2px;
    position: absolute;
    bottom: 1vh;
    left: 50%;
    width: 100%;
    margin-left: calc(50% - 5.1vh);
    transform: translate(-50%)
}

.item-notification-action-box {
    width: 100%;
    color: #fff;
    background-color: transparent;
    text-align: center;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    font-family: gilroy-regular, sans-serif
}

.item-notification-action-box p {
    font-size: 11px;
    padding: 2px;
    font-weight: 600
}

.item-notification-item-box {
    height: 9vh;
    width: 9vh
}

.durability-bar {
    background: rgba(0, 0, 0, .5);
    height: 0px;
    overflow: hidden
}

.weight-bar {
    position: absolute;
    width: 30px;
    height: 30px;
    background: #000;
    background: url("https://336332c778dbcc9f00ef31bbead5776c9f66994&");
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    border-radius: 2px;
    margin-left: 460px;
    margin-top: -28px
}

.transition-fade-enter {
    opacity: 1
}

.transition-fade-enter-active {
    opacity: 1;
    transition: opacity .2s
}

.transition-fade-exit {
    opacity: 1
}

.transition-fade-exit-active {
    opacity: 1;
    transition: opacity .2s
}

.transition-slide-up-enter {
    transform: translatey(200px)
}

.transition-slide-up-enter-active {
    transform: translatey(0);
    transition: all .2s
}

.transition-slide-up-exit {
    transform: translatey(0)
}

.transition-slide-up-exit-active {
    transform: translatey(200px);
    transition: all .2s
}

.RegularSlots {
    display: grid;
    height: calc(52.1vh + 10px);
    grid-template-columns: repeat(5, 100px);
    grid-auto-rows: 100px;
    gap: 5px
}

.ClothingSlots {
    gap: 46px;
    display: flex;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 50%;
    overflow: auto;
    position: fixed
}

.Clothing1,
.Clothing2 {
    display: grid;
    grid-template-columns: 80px;
    grid-auto-rows: 80px;
    gap: 15px
}

.Clothing3 {
    display: grid;
    grid-template-columns: 75px;
    grid-auto-rows: 75px;
    gap: 8px
}

.WeightText {
    font-size: 14px;
    font-family: robotocondensedmedium;
    padding-left: 10px;
    margin-bottom: 6px
}

.LabelText {
    font-size: 20px;
    font-family: gilroy-light, sans-serif;
    padding-right: 10px
}

.HotSlotLabel {
    position: absolute;
    bottom: 34.4vh;
    font-size: 21px;
    font-family: robotocondensedlight
}

.iconWrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #008cff;
    padding: 6px;
    border-radius: 5px;
    margin-right: 12px;
    box-shadow: 0 2px 20px 4px #008cff7d;
    width: 20px;
    height: 20px
}

.iconWrapper2 {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #008cff;
    padding: 6px;
    border-radius: 5px;
    margin-right: 12px;
    box-shadow: 0 2px 20px 4px #008cff7d;
    width: 25px;
    height: 25px
}

.iconWrapper svg {
    font-size: 15px
}


.WeightBarWrap {
  position: absolute;
  left: 3.2%;
  top: 10%;
  width: 200px;
  background: no-repeat url(https://i.ibb.co/QNF040W/Group3.png);
  height: 100px;
  width: 480px;
  flex: 1;
  padding-bottom: 8px
    
}

.WeightBarWrap2 {
    height: 5px;
    flex: 1;
    padding-bottom: 8px
}

@media screen and (max-width:1700px) {
    .LabelName {
        width: 32.5vw
    }
}

@media screen and (max-width:1400px) {
    .RegularSlots {
        grid-template-columns: repeat(5, 70px);
        grid-auto-rows: 70px
    }

    .LabelName {
        width: 31.5vw
    }

    .Clothing1,
    .Clothing2 {
        grid-template-columns: 58px;
        grid-auto-rows: 58px
    }

    .inventory-grid-container {
        grid-template-columns: repeat(5, 65px);
        grid-auto-rows: 65px
    }

    .inventory-grid-container-user {
        width: 53rem
    }

    .inventory-slot-number {
        height: 7px;
        width: 7px;
        font-size: 6px
    }
}

@media screen and (min-width:2500px) {
    .LabelName {
        width: 20.5vw
    }
}

@media screen and (min-width:3400px) {
    .LabelName {
        width: 15vw
    }
}